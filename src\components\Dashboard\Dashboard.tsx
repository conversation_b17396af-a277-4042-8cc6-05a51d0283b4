import { useState, useEffect } from 'react';
import { Calculator, AlertTriangle, Settings, Search, Target } from 'lucide-react';
import { Commune, CalculResult, InterventionParams, SimulatorSettings, SaveSimulationData } from '../../types';
import { LocationSelector, LocationData } from './LocationSelector';
import { ResultsPanel } from './ResultsPanel';
import { InterventionMap } from './InterventionMap';
import { SaveSimulationModal } from './SaveSimulationModal';
import { calculerMoyensAeriens, calculerMoyensAeriensParCoordonnees } from '../../services/calculService';
import { loadUserSettings } from '../../services/settingsService';
import { saveSimulation } from '../../services/simulationService';

// Fonction pour convertir km/h en nœuds
function convertKmhToKnots(kmh: number): number {
  // 1 nœud = 1,852 km/h
  return kmh / 1.852;
}

function formatSpeed(kmh: number): string {
  const knots = convertKmhToKnots(kmh);
  return `${Math.round(knots)} kts (${kmh} km/h)`;
}

// Paramètres par défaut si aucun paramètre utilisateur n'est trouvé
const defaultSettings: SimulatorSettings = {
  canadair: {
    vitesseCroisiere: 300,
    vitesseMaxLargage: 250,
    vitesseMinLargage: 180,
    capaciteReservoir: 6000,
    tempsRemplissage: 1,
    rayonBraquage: 800,
    altitudeMaxLargage: 150,
    altitudeMinLargage: 30,
    consommationCarburant: 1200,
    autonomieVol: 4.5
  },
  dash: {
    vitesseCroisiere: 400,
    vitesseMaxLargage: 320,
    vitesseMinLargage: 220,
    capaciteReservoir: 3000,
    tempsRemplissage: 3,
    rayonBraquage: 600,
    altitudeMaxLargage: 200,
    altitudeMinLargage: 50,
    consommationCarburant: 800,
    autonomieVol: 6
  },
  facteurVent: 1.0,
  facteurVisibilite: 1.0,
  tempsPreparationMission: 15,
  tempsRetourBase: 10,
  margeSecurite: 20,
  // Nouveaux paramètres opérationnels
  exigencesCanadair: 20,
  exigencesDash: 5,
  dureeMission: 60
};

export function Dashboard() {
  const [selectedLocation, setSelectedLocation] = useState<LocationData>();
  const [results, setResults] = useState<CalculResult>();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [settings, setSettings] = useState<SimulatorSettings>(defaultSettings);
  const [settingsLoading, setSettingsLoading] = useState(true);
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);

  // Charger les paramètres utilisateur au démarrage
  useEffect(() => {
    const loadSettings = async () => {
      try {
        setSettingsLoading(true);
        const userSettings = await loadUserSettings();
        if (userSettings) {
          // Fusionner avec les paramètres par défaut pour gérer les nouveaux paramètres
          setSettings({
            ...defaultSettings,
            ...userSettings,
            // S'assurer que les nouveaux paramètres ont des valeurs par défaut
            exigencesCanadair: userSettings.exigencesCanadair ?? defaultSettings.exigencesCanadair,
            exigencesDash: userSettings.exigencesDash ?? defaultSettings.exigencesDash,
            dureeMission: userSettings.dureeMission ?? defaultSettings.dureeMission
          });
        }
      } catch (error) {
        console.error('Erreur lors du chargement des paramètres:', error);
        // Utiliser les paramètres par défaut en cas d'erreur
      } finally {
        setSettingsLoading(false);
      }
    };

    loadSettings();
  }, []);

  const handleLocationSelect = async (location: LocationData) => {
    setSelectedLocation(location);
    setLoading(true);
    setError(null);

    try {
      let calculResults: CalculResult;

      if (location.type === 'commune' && location.commune) {
        // Paramètres selon les paramètres utilisateur pour commune
        const params: InterventionParams = {
          communeId: location.commune.id,
          typeIntervention: 'incendie_foret',
          exigencesCanadair: settings.exigencesCanadair,
          exigencesDash: settings.exigencesDash
        };

        calculResults = await calculerMoyensAeriens(location.commune, params, settings);
      } else if (location.type === 'coordinates' && location.coordinates) {
        // Paramètres pour coordonnées GPS
        const params = {
          typeIntervention: 'incendie_foret' as const,
          exigencesCanadair: settings.exigencesCanadair,
          exigencesDash: settings.exigencesDash
        };

        calculResults = await calculerMoyensAeriensParCoordonnees(
          location.coordinates.latitude,
          location.coordinates.longitude,
          params,
          settings
        );
      } else {
        throw new Error('Type de localisation non supporté');
      }

      setResults(calculResults);
    } catch (err) {
      console.error('Erreur lors du calcul:', err);
      setError('Erreur lors du calcul des moyens aériens. Vérifiez que les données sont bien configurées dans Supabase.');
    } finally {
      setLoading(false);
    }
  };

  const handleSaveSimulation = async (data: { nom: string; description?: string }) => {
    if (!selectedLocation || !results) return;

    setSaveLoading(true);
    try {
      // Créer les données de simulation selon le type de localisation
      let simulationData: SaveSimulationData;

      if (selectedLocation.type === 'commune' && selectedLocation.commune) {
        const params: InterventionParams = {
          communeId: selectedLocation.commune.id,
          typeIntervention: 'incendie_foret',
          exigencesCanadair: settings.exigencesCanadair,
          exigencesDash: settings.exigencesDash
        };

        simulationData = {
          nom: data.nom,
          description: data.description,
          commune_data: selectedLocation.commune,
          intervention_params: params,
          simulator_settings: settings,
          results: results
        };
      } else if (selectedLocation.type === 'coordinates' && selectedLocation.coordinates) {
        // Pour les coordonnées, créer une commune virtuelle
        const virtualCommune = {
          id: `virtual_${selectedLocation.coordinates.latitude}_${selectedLocation.coordinates.longitude}`,
          nom: 'Position GPS',
          codePostal: '',
          latitude: selectedLocation.coordinates.latitude,
          longitude: selectedLocation.coordinates.longitude,
          departement: 'Position personnalisée'
        };

        const params: InterventionParams = {
          communeId: virtualCommune.id,
          typeIntervention: 'incendie_foret',
          exigencesCanadair: settings.exigencesCanadair,
          exigencesDash: settings.exigencesDash
        };

        simulationData = {
          nom: data.nom,
          description: data.description,
          commune_data: virtualCommune,
          intervention_params: params,
          simulator_settings: settings,
          results: results
        };
      } else {
        throw new Error('Type de localisation non supporté pour la sauvegarde');
      }

      await saveSimulation(simulationData);
      // Optionnel: afficher un message de succès
    } catch (err) {
      console.error('Erreur lors de la sauvegarde:', err);
      throw err; // Le modal gérera l'erreur
    } finally {
      setSaveLoading(false);
    }
  };

  // Recalculer si les paramètres changent et qu'une localisation est sélectionnée
  useEffect(() => {
    if (selectedLocation && !settingsLoading) {
      handleLocationSelect(selectedLocation);
    }
  }, [settings, settingsLoading]);

  if (settingsLoading) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-md p-12 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Chargement des paramètres...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-8 relative z-30 bg-white py-4 -mb-2">
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Estimation des moyens aériens</h2>
        <p className="text-gray-600">
          Sélectionnez une position (commune ou coordonnées GPS) pour calculer automatiquement les besoins en Canadair et Dash
        </p>
      </div>

      <div className="flex flex-col lg:flex-row gap-8">
        {/* Panneau de gauche - Paramètres (1/3) */}
        <div className="lg:w-1/3">
          <div className="bg-white rounded-lg shadow-md p-6 space-y-6">
            <h3 className="text-lg font-semibold text-gray-800 flex items-center space-x-2">
              <Calculator className="h-5 w-5 text-red-600" />
              <span>Paramètres d'intervention</span>
            </h3>

            <LocationSelector
              onLocationSelect={handleLocationSelect}
              selectedLocation={selectedLocation}
            />

            {/* Indicateur de méthode active */}
            {selectedLocation && (
              <div className={`p-3 rounded-lg border ${
                selectedLocation.type === 'commune'
                  ? 'bg-green-50 border-green-200'
                  : 'bg-red-50 border-red-200'
              }`}>
                <div className="flex items-center space-x-2">
                  {selectedLocation.type === 'commune' ? (
                    <>
                      <Search className="h-4 w-4 text-green-600" />
                      <span className="text-green-800 font-medium text-sm">
                        Méthode active: Recherche par commune
                      </span>
                    </>
                  ) : (
                    <>
                      <Target className="h-4 w-4 text-red-600" />
                      <span className="text-red-800 font-medium text-sm">
                        Méthode active: Coordonnées GPS
                      </span>
                    </>
                  )}
                </div>
                <p className="text-xs mt-1 text-gray-600">
                  {selectedLocation.displayName}
                </p>
              </div>
            )}

            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-800 mb-2 flex items-center space-x-2">
                <Settings className="h-4 w-4" />
                <span>Paramètres actifs</span>
              </h4>
              <div className="text-sm text-blue-700 space-y-1">
                <div>• Vitesse Canadair: {formatSpeed(settings.canadair.vitesseCroisiere)}</div>
                <div>• Forfait écopage canadair: {settings.canadair.tempsRemplissage} min</div>
                <div>• Vitesse Dash: {formatSpeed(settings.dash.vitesseCroisiere)}</div>
                <div>• Ravitaillement Dash: {settings.dash.tempsRemplissage} min</div>
                <div>• Facteur vent: {settings.facteurVent}</div>
                <div>• Marge sécurité: {settings.margeSecurite}%</div>
              </div>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h4 className="font-medium text-yellow-800 mb-2 flex items-center space-x-2">
                <AlertTriangle className="h-4 w-4" />
                <span>Objectifs opérationnels</span>
              </h4>
              <div className="text-sm text-yellow-700 space-y-1">
                <div>• Exigence Canadair: {settings.exigencesCanadair} largages par heure</div>
                <div>• Exigence Dash: {settings.exigencesDash} largages par heure</div>
              </div>
            </div>

            {error && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <h4 className="font-medium text-red-800 mb-2">Erreur</h4>
                <p className="text-sm text-red-700">{error}</p>
              </div>
            )}
          </div>
        </div>

        {/* Panneau de droite - Carte et résultats (2/3) */}
        <div className="lg:w-2/3 lg:flex-1 sticky-parent">
          {/* Carte */}
          <div className="sticky-map-container mb-6">
            {results ? (
              <InterventionMap results={results} height={500} />
            ) : selectedLocation ? (
              <InterventionMap
                height={500}
                clickable={true}
                onMapClick={(lat, lng) => {
                  const locationData = {
                    type: 'coordinates' as const,
                    coordinates: { latitude: lat, longitude: lng },
                    displayName: `Position GPS (${lat.toFixed(6)}, ${lng.toFixed(6)})`
                  };
                  handleLocationSelect(locationData);
                }}
                selectedCoordinates={selectedLocation.coordinates}
              />
            ) : (
              <InterventionMap
                height={500}
                clickable={true}
                onMapClick={(lat, lng) => {
                  const locationData = {
                    type: 'coordinates' as const,
                    coordinates: { latitude: lat, longitude: lng },
                    displayName: `Position GPS (${lat.toFixed(6)}, ${lng.toFixed(6)})`
                  };
                  handleLocationSelect(locationData);
                }}
              />
            )}
          </div>

          {/* Résultats sous la carte */}
          {loading ? (
            <div className="bg-white rounded-lg shadow-md p-12 text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Calcul en cours...</p>
            </div>
          ) : results ? (
            <ResultsPanel
              results={results}
              onSaveSimulation={() => setShowSaveModal(true)}
            />
          ) : selectedLocation ? (
            <div className="bg-gray-50 border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
              <Calculator className="mx-auto h-8 w-8 text-gray-400 mb-3" />
              <h3 className="text-base font-medium text-gray-900 mb-2">
                Position sélectionnée
              </h3>
              <p className="text-gray-600 text-sm">
                Cliquez sur "Définir la position du feu" pour lancer le calcul
              </p>
            </div>
          ) : null}
        </div>
      </div>

      {/* Modal de sauvegarde */}
      {showSaveModal && selectedLocation && results && (
        <SaveSimulationModal
          isOpen={showSaveModal}
          onClose={() => setShowSaveModal(false)}
          onSave={handleSaveSimulation}
          simulationData={{
            nom: '',
            commune_data: selectedLocation.type === 'commune' && selectedLocation.commune
              ? selectedLocation.commune
              : {
                  id: `virtual_${selectedLocation.coordinates?.latitude}_${selectedLocation.coordinates?.longitude}`,
                  nom: 'Position GPS',
                  codePostal: '',
                  latitude: selectedLocation.coordinates?.latitude || 0,
                  longitude: selectedLocation.coordinates?.longitude || 0,
                  departement: 'Position personnalisée'
                },
            intervention_params: {
              communeId: selectedLocation.type === 'commune' && selectedLocation.commune
                ? selectedLocation.commune.id
                : `virtual_${selectedLocation.coordinates?.latitude}_${selectedLocation.coordinates?.longitude}`,
              typeIntervention: 'incendie_foret',
              exigencesCanadair: settings.exigencesCanadair,
              exigencesDash: settings.exigencesDash
            },
            simulator_settings: settings,
            results: results
          }}
          loading={saveLoading}
        />
      )}
    </div>
  );
}