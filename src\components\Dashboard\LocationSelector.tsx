import React, { useState } from 'react';
import { Search, Target } from 'lucide-react';
import { Commune } from '../../types';
import { CommuneSearch } from './CommuneSearch';
import { CoordinateInput } from './CoordinateInput';

type LocationInputMethod = 'commune' | 'coordinates';

interface LocationSelectorProps {
  onLocationSelect: (location: LocationData) => void;
  selectedLocation?: LocationData;
}

export interface LocationData {
  type: 'commune' | 'coordinates';
  commune?: Commune;
  coordinates?: { latitude: number; longitude: number };
  displayName: string;
}

export function LocationSelector({ onLocationSelect, selectedLocation }: LocationSelectorProps) {
  const [activeMethod, setActiveMethod] = useState<LocationInputMethod>('commune');
  const [selectedCoordinates, setSelectedCoordinates] = useState<{ latitude: number; longitude: number } | undefined>(
    selectedLocation?.coordinates
  );

  const handleCommuneSelect = (commune: Commune) => {
    const locationData: LocationData = {
      type: 'commune',
      commune,
      displayName: `${commune.nom} (${commune.codePostal})`
    };
    setSelectedCoordinates(undefined);
    onLocationSelect(locationData);
  };

  const handleCoordinateSelect = (latitude: number, longitude: number) => {
    const coordinates = { latitude, longitude };
    const locationData: LocationData = {
      type: 'coordinates',
      coordinates,
      displayName: `Position GPS (${latitude.toFixed(6)}, ${longitude.toFixed(6)})`
    };
    setSelectedCoordinates(coordinates);
    onLocationSelect(locationData);
  };

  const inputMethods = [
    {
      id: 'commune' as LocationInputMethod,
      label: 'Recherche par commune',
      icon: Search,
      description: 'Sélectionner une commune existante'
    },
    {
      id: 'coordinates' as LocationInputMethod,
      label: 'Coordonnées GPS',
      icon: Target,
      description: 'Saisir latitude et longitude'
    }
  ];

  return (
    <div className="space-y-6">
      {/* Sélecteur de méthode d'entrée */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-3">
          Méthode de sélection de la position du feu
        </label>
        <div className="mb-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-sm text-blue-700">
            💡 <strong>Astuce :</strong> Vous pouvez aussi cliquer directement sur la carte à droite pour définir la position du feu
          </p>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
          {inputMethods.map((method) => {
            const Icon = method.icon;
            return (
              <button
                key={method.id}
                onClick={() => setActiveMethod(method.id)}
                className={`p-3 border rounded-lg text-left transition-colors ${
                  activeMethod === method.id
                    ? 'border-red-500 bg-red-50 text-red-700'
                    : 'border-gray-300 hover:border-gray-400 text-gray-700'
                }`}
              >
                <div className="flex items-center space-x-2 mb-1">
                  <Icon className="h-4 w-4" />
                  <span className="font-medium text-sm">{method.label}</span>
                </div>
                <p className="text-xs text-gray-600">{method.description}</p>
              </button>
            );
          })}
        </div>
      </div>

      {/* Interface de saisie selon la méthode sélectionnée */}
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        {activeMethod === 'commune' && (
          <CommuneSearch
            onCommuneSelect={handleCommuneSelect}
            selectedCommune={selectedLocation?.commune}
          />
        )}

        {activeMethod === 'coordinates' && (
          <CoordinateInput
            onCoordinateSelect={handleCoordinateSelect}
            selectedCoordinates={selectedCoordinates}
          />
        )}
      </div>

      {/* Affichage de la sélection actuelle */}
      {selectedLocation && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4">
          <h4 className="font-medium text-green-800 mb-2">Position sélectionnée</h4>
          <div className="text-sm text-green-700">
            <p><strong>Méthode:</strong> {selectedLocation.type === 'commune' ? 'Commune' : 'Coordonnées GPS'}</p>
            <p><strong>Position:</strong> {selectedLocation.displayName}</p>
            {selectedLocation.coordinates && (
              <p><strong>Coordonnées:</strong> {selectedLocation.coordinates.latitude.toFixed(6)}, {selectedLocation.coordinates.longitude.toFixed(6)}</p>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
