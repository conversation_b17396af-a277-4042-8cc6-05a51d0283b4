@tailwind base;
@tailwind components;
@tailwind utilities;

/* Sticky map styles */
.sticky-map-container {
  position: -webkit-sticky;
  position: sticky;
  top: 2rem;
  z-index: 10;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  align-self: flex-start;
}

/* Assurer que le conteneur parent a une hauteur suffisante */
.sticky-parent {
  min-height: 100vh;
}

@media (max-width: 1024px) {
  .sticky-map-container {
    position: static;
    box-shadow: none;
    align-self: auto;
  }
}

/* Custom slider styles */
.slider::-webkit-slider-thumb {
  appearance: none;
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #DC2626;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
  height: 20px;
  width: 20px;
  border-radius: 50%;
  background: #DC2626;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}