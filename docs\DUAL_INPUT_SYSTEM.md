# Dual-Input System for Fire Location Selection

## Overview

The FireSplash application has been enhanced with a comprehensive dual-input system that allows users to define fire locations using two methods:

1. **Commune Selection** - Traditional dropdown search for existing communes
2. **GPS Coordinates** - Direct latitude/longitude input or map clicking

## Features Implemented

### 🎯 Core Components

#### 1. CoordinateInput Component (`src/components/Dashboard/CoordinateInput.tsx`)
- Manual latitude/longitude input with real-time validation
- Supports decimal format (e.g., 43.7750, 6.2000)
- Input validation: Latitude (-90 to 90), Longitude (-180 to 180)
- Visual feedback for selected coordinates
- Clear error messages for invalid inputs

#### 2. Enhanced InterventionMap Component (`src/components/Dashboard/InterventionMap.tsx`)
- **NEW**: Map click functionality with `onMapClick` callback
- **NEW**: `clickable` prop to enable/disable map interaction
- **NEW**: `selectedCoordinates` prop for displaying GPS-selected locations
- Visual feedback with fire emoji marker for clicked locations
- Crosshair cursor when in clickable mode
- Helpful legend with click instructions

#### 3. LocationSelector Component (`src/components/Dashboard/LocationSelector.tsx`)
- Unified interface combining all three input methods:
  - Commune search (existing functionality)
  - GPS coordinate input (new)
  - Map clicking (new)
- Tabbed interface with clear method selection
- Real-time switching between input methods
- Consistent data structure for all location types

### 🔧 Backend Enhancements

#### Enhanced Calculation Service (`src/services/calculService.ts`)
- **NEW**: `calculerMoyensAeriensParCoordonnees()` function
- **NEW**: `createVirtualCommune()` helper function
- Seamless integration with existing calculation algorithms
- Virtual commune creation for GPS coordinates
- Maintains compatibility with existing commune-based calculations

### 🎨 User Experience Improvements

#### Dashboard Integration (`src/components/Dashboard/Dashboard.tsx`)
- Completely refactored to use the new dual-input system
- **NEW**: Active method indicator showing current input type
- **NEW**: Location display with method and coordinates
- Updated text and labels for clarity
- Maintains all existing functionality (settings, save, etc.)

#### Visual Indicators
- Color-coded method indicators (green for commune, red for GPS)
- Clear display of selected location information
- Real-time feedback for active input method
- Helpful tooltips and instructions

## Technical Architecture

### Data Flow
```
User Input → LocationSelector → LocationData → Dashboard → CalculationService → Results
```

### LocationData Interface
```typescript
interface LocationData {
  type: 'commune' | 'coordinates';
  commune?: Commune;
  coordinates?: { latitude: number; longitude: number };
  displayName: string;
}
```

### Virtual Commune Creation
For GPS coordinates, the system creates virtual commune objects:
```typescript
{
  id: `virtual_${latitude}_${longitude}`,
  nom: 'Position GPS',
  codePostal: '',
  latitude: latitude,
  longitude: longitude,
  departement: 'Position personnalisée'
}
```

## Usage Instructions

### For Users

1. **Commune Selection** (Traditional method)
   - Click "Recherche par commune" tab
   - Type commune name in search field
   - Select from dropdown suggestions

2. **GPS Coordinates** (New method)
   - Click "Coordonnées GPS" tab
   - Enter latitude and longitude in decimal format
   - Click "Définir la position du feu"

3. **Map Clicking** (New method)
   - Click "Clic sur carte" tab
   - Click anywhere on the map to set fire location
   - Coordinates automatically populate

### For Developers

#### Adding New Input Methods
1. Extend `LocationInputMethod` type in `LocationSelector.tsx`
2. Add new method to `inputMethods` array
3. Implement UI in the method selection switch
4. Update `LocationData` interface if needed

#### Extending Calculation Service
The calculation service now supports both commune and coordinate inputs:

```typescript
// For communes (existing)
await calculerMoyensAeriens(commune, params, settings);

// For coordinates (new)
await calculerMoyensAeriensParCoordonnees(lat, lon, params, settings);
```

## Testing

### Manual Testing
1. Start development server: `npm run dev`
2. Navigate to Dashboard
3. Test all three input methods:
   - Search for a commune
   - Enter GPS coordinates manually
   - Click on the map
4. Verify calculations work for all methods
5. Test save functionality with both input types

### Automated Testing
Test functions available in `src/test/coordinateCalculationTest.ts`:
- `testCoordinateCalculation()` - Tests GPS coordinate calculations
- `testVirtualCommuneCreation()` - Tests virtual commune creation

## Compatibility

### Backward Compatibility
- ✅ All existing commune-based functionality preserved
- ✅ Existing saved simulations continue to work
- ✅ Settings and configuration unchanged
- ✅ API endpoints and data structures maintained

### Browser Support
- ✅ Modern browsers with ES6+ support
- ✅ Leaflet map library compatibility
- ✅ Touch devices supported for map clicking

## Future Enhancements

### Potential Improvements
1. **Geocoding Integration** - Convert addresses to coordinates
2. **Coordinate Format Support** - Support DMS format (degrees/minutes/seconds)
3. **Location History** - Remember recently used coordinates
4. **Bulk Import** - Import multiple coordinates from CSV/Excel
5. **Map Layers** - Add satellite/terrain view options
6. **Coordinate Validation** - Check if coordinates are over land/accessible areas

### Performance Optimizations
1. **Debounced Map Clicks** - Prevent accidental double-clicks
2. **Coordinate Caching** - Cache calculation results for repeated coordinates
3. **Map Tile Optimization** - Implement tile caching for better performance

## Conclusion

The dual-input system successfully enhances the FireSplash application by providing users with flexible options for defining fire locations while maintaining full compatibility with existing functionality. The implementation follows React best practices and provides a seamless user experience across all input methods.
