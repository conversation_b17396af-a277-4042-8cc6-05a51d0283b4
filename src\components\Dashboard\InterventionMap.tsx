import React, { useEffect, useRef } from 'react';
import { CalculResult } from '../../types';

interface InterventionMapProps {
  results?: CalculResult;
  height?: number;
  onMapClick?: (latitude: number, longitude: number) => void;
  clickable?: boolean;
  selectedCoordinates?: { latitude: number; longitude: number };
}

export function InterventionMap({
  results,
  height = 500,
  onMapClick,
  clickable = false,
  selectedCoordinates
}: InterventionMapProps) {
  const mapRef = useRef<HTMLDivElement>(null);
  const mapInstanceRef = useRef<any>(null);
  const clickMarkerRef = useRef<any>(null);

  useEffect(() => {
    if (!mapRef.current) return;

    // Initialiser la carte
    const map = (window as any).L.map(mapRef.current).setView([43.8367, 4.3601], 8);
    mapInstanceRef.current = map;

    // Ajouter les tuiles OpenStreetMap
    (window as any).L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
      attribution: '© OpenStreetMap contributors'
    }).addTo(map);

    // Ajouter le gestionnaire de clic si la carte est cliquable
    if (clickable && onMapClick) {
      map.on('click', (e: any) => {
        const { lat, lng } = e.latlng;
        onMapClick(lat, lng);
      });

      // Changer le curseur pour indiquer que la carte est cliquable
      map.getContainer().style.cursor = 'crosshair';
    }

    return () => {
      if (mapInstanceRef.current) {
        mapInstanceRef.current.remove();
      }
    };
  }, [clickable, onMapClick]);

  // Effet pour gérer le marqueur de coordonnées sélectionnées
  useEffect(() => {
    if (!mapInstanceRef.current) return;

    const map = mapInstanceRef.current;
    const L = (window as any).L;

    // Supprimer le marqueur de clic précédent
    if (clickMarkerRef.current) {
      map.removeLayer(clickMarkerRef.current);
      clickMarkerRef.current = null;
    }

    // Ajouter un marqueur pour les coordonnées sélectionnées (sans résultats)
    if (selectedCoordinates && !results) {
      const fireIcon = L.divIcon({
        html: '<div style="background-color: #DC2626; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; font-size: 14px;">🔥</div>',
        className: 'custom-fire-icon',
        iconSize: [24, 24],
        iconAnchor: [12, 12]
      });

      clickMarkerRef.current = L.marker([selectedCoordinates.latitude, selectedCoordinates.longitude], { icon: fireIcon })
        .addTo(map)
        .bindPopup(`<strong>Position du feu</strong><br/>Lat: ${selectedCoordinates.latitude.toFixed(6)}<br/>Lon: ${selectedCoordinates.longitude.toFixed(6)}`);

      // Centrer la carte sur les coordonnées sélectionnées
      map.setView([selectedCoordinates.latitude, selectedCoordinates.longitude], 10);
    }
  }, [selectedCoordinates, results]);

  useEffect(() => {
    if (!results || !mapInstanceRef.current) return;

    const map = mapInstanceRef.current;
    const L = (window as any).L;

    // Nettoyer les marqueurs existants
    map.eachLayer((layer: any) => {
      if (layer instanceof L.Marker || layer instanceof L.Polyline) {
        map.removeLayer(layer);
      }
    });

    // Créer les icônes personnalisées
    const fireIcon = L.divIcon({
      html: '<div style="background-color: #DC2626; color: white; border-radius: 50%; width: 24px; height: 24px; display: flex; align-items: center; justify-content: center; font-size: 14px;">🔥</div>',
      className: 'custom-fire-icon',
      iconSize: [24, 24],
      iconAnchor: [12, 12]
    });

    const waterIcon = L.divIcon({
      html: '<div style="background-color: #2563EB; color: white; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 12px;">💧</div>',
      className: 'custom-water-icon',
      iconSize: [20, 20],
      iconAnchor: [10, 10]
    });

    const baseIcon = L.divIcon({
      html: '<div style="background-color: #059669; color: white; border-radius: 50%; width: 20px; height: 20px; display: flex; align-items: center; justify-content: center; font-size: 12px;">✈️</div>',
      className: 'custom-base-icon',
      iconSize: [20, 20],
      iconAnchor: [10, 10]
    });

    // Ajouter les marqueurs
    const fireMarker = L.marker([results.commune.latitude, results.commune.longitude], { icon: fireIcon })
      .addTo(map)
      .bindPopup(`<strong>Zone d'intervention</strong><br/>${results.commune.nom}`);

    const waterMarker = L.marker([results.planEauProche.latitude, results.planEauProche.longitude], { icon: waterIcon })
      .addTo(map)
      .bindPopup(`<strong>Plan d'eau</strong><br/>${results.planEauProche.nom}<br/>Distance: ${results.distanceEau} km`);

    const baseMarker = L.marker([results.baseDashProche.latitude, results.baseDashProche.longitude], { icon: baseIcon })
      .addTo(map)
      .bindPopup(`<strong>Base Dash</strong><br/>${results.baseDashProche.nom}<br/>Distance: ${results.distanceDash} km`);

    // Ajouter les lignes
    const waterLine = L.polyline([
      [results.commune.latitude, results.commune.longitude],
      [results.planEauProche.latitude, results.planEauProche.longitude]
    ], { color: '#2563EB', weight: 3, opacity: 0.7 }).addTo(map);

    const baseLine = L.polyline([
      [results.commune.latitude, results.commune.longitude],
      [results.baseDashProche.latitude, results.baseDashProche.longitude]
    ], { color: '#059669', weight: 3, opacity: 0.7 }).addTo(map);

    // Ajuster la vue pour inclure tous les points
    const group = L.featureGroup([fireMarker, waterMarker, baseMarker]);
    map.fitBounds(group.getBounds().pad(0.1));

  }, [results]);

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden">
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-800">Visualisation cartographique</h3>
      </div>
      
      <div ref={mapRef} style={{ height: `${height}px`, width: '100%' }} />
      
      {/* Légende */}
      <div className="p-4 bg-gray-50 border-t border-gray-200">
        <h4 className="font-medium text-gray-800 mb-2">Légende</h4>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-3 text-sm">
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 bg-red-600 rounded-full flex items-center justify-center text-white text-xs">🔥</div>
            <span>Zone d'intervention</span>
          </div>
          {results && (
            <>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-blue-600 rounded-full flex items-center justify-center text-white text-xs">💧</div>
                <span>Plan d'eau</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-4 bg-green-600 rounded-full flex items-center justify-center text-white text-xs">✈️</div>
                <span>Pélicandrome</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-1 bg-blue-600"></div>
                <span>Trajet Canadair</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-4 h-1 bg-green-600"></div>
                <span>Trajet Dash</span>
              </div>
            </>
          )}
        </div>
        {clickable && (
          <div className="mt-3 p-2 bg-blue-50 border border-blue-200 rounded text-xs text-blue-700">
            💡 Cliquez sur la carte pour définir la position du feu
          </div>
        )}
      </div>
    </div>
  );
}