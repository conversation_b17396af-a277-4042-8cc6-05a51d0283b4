import React, { useState, useEffect } from 'react';
import { MapPin, Target } from 'lucide-react';

interface CoordinateInputProps {
  onCoordinateSelect: (latitude: number, longitude: number) => void;
  selectedCoordinates?: { latitude: number; longitude: number };
  disabled?: boolean;
}

export function CoordinateInput({ onCoordinateSelect, selectedCoordinates, disabled = false }: CoordinateInputProps) {
  const [latitude, setLatitude] = useState('');
  const [longitude, setLongitude] = useState('');
  const [errors, setErrors] = useState<{ latitude?: string; longitude?: string }>({});

  // Synchroniser avec les coordonnées sélectionnées
  useEffect(() => {
    if (selectedCoordinates) {
      setLatitude(selectedCoordinates.latitude.toString());
      setLongitude(selectedCoordinates.longitude.toString());
      setErrors({});
    }
  }, [selectedCoordinates]);

  const validateCoordinate = (value: string, type: 'latitude' | 'longitude'): string | undefined => {
    if (!value.trim()) {
      return undefined; // Pas d'erreur si vide
    }

    const num = parseFloat(value);
    if (isNaN(num)) {
      return 'Doit être un nombre valide';
    }

    if (type === 'latitude') {
      if (num < -90 || num > 90) {
        return 'Latitude doit être entre -90 et 90';
      }
    } else {
      if (num < -180 || num > 180) {
        return 'Longitude doit être entre -180 et 180';
      }
    }

    return undefined;
  };

  const handleLatitudeChange = (value: string) => {
    setLatitude(value);
    const error = validateCoordinate(value, 'latitude');
    setErrors(prev => ({ ...prev, latitude: error }));
  };

  const handleLongitudeChange = (value: string) => {
    setLongitude(value);
    const error = validateCoordinate(value, 'longitude');
    setErrors(prev => ({ ...prev, longitude: error }));
  };

  const handleSubmit = () => {
    const latError = validateCoordinate(latitude, 'latitude');
    const lonError = validateCoordinate(longitude, 'longitude');

    if (latError || lonError) {
      setErrors({ latitude: latError, longitude: lonError });
      return;
    }

    if (!latitude.trim() || !longitude.trim()) {
      setErrors({
        latitude: !latitude.trim() ? 'Latitude requise' : undefined,
        longitude: !longitude.trim() ? 'Longitude requise' : undefined
      });
      return;
    }

    const lat = parseFloat(latitude);
    const lon = parseFloat(longitude);
    onCoordinateSelect(lat, lon);
  };

  const isValid = latitude.trim() && longitude.trim() && !errors.latitude && !errors.longitude;

  return (
    <div className="space-y-4">
      <label className="block text-sm font-medium text-gray-700">
        Coordonnées GPS
      </label>
      
      <div className="grid grid-cols-2 gap-3">
        <div>
          <label className="block text-xs font-medium text-gray-600 mb-1">
            Latitude
          </label>
          <input
            type="number"
            step="any"
            value={latitude}
            onChange={(e) => handleLatitudeChange(e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 ${
              errors.latitude ? 'border-red-300 bg-red-50' : 'border-gray-300'
            } ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
            placeholder="Ex: 43.7750"
            disabled={disabled}
          />
          {errors.latitude && (
            <p className="text-xs text-red-600 mt-1">{errors.latitude}</p>
          )}
        </div>

        <div>
          <label className="block text-xs font-medium text-gray-600 mb-1">
            Longitude
          </label>
          <input
            type="number"
            step="any"
            value={longitude}
            onChange={(e) => handleLongitudeChange(e.target.value)}
            className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 focus:border-red-500 ${
              errors.longitude ? 'border-red-300 bg-red-50' : 'border-gray-300'
            } ${disabled ? 'bg-gray-100 cursor-not-allowed' : ''}`}
            placeholder="Ex: 6.2000"
            disabled={disabled}
          />
          {errors.longitude && (
            <p className="text-xs text-red-600 mt-1">{errors.longitude}</p>
          )}
        </div>
      </div>

      <button
        onClick={handleSubmit}
        disabled={!isValid || disabled}
        className={`w-full flex items-center justify-center space-x-2 px-4 py-2 rounded-md font-medium transition-colors ${
          isValid && !disabled
            ? 'bg-red-600 text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2'
            : 'bg-gray-300 text-gray-500 cursor-not-allowed'
        }`}
      >
        <Target className="h-4 w-4" />
        <span>Définir la position du feu</span>
      </button>

      {selectedCoordinates && (
        <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded-md">
          <div className="flex items-center space-x-2">
            <MapPin className="h-4 w-4 text-red-600" />
            <span className="text-red-800 font-medium text-sm">
              Position: {selectedCoordinates.latitude.toFixed(6)}, {selectedCoordinates.longitude.toFixed(6)}
            </span>
          </div>
        </div>
      )}

      <div className="text-xs text-gray-500">
        <p>• Format décimal requis (ex: 43.7750, 6.2000)</p>
        <p>• Latitude: -90 à 90, Longitude: -180 à 180</p>
      </div>
    </div>
  );
}
