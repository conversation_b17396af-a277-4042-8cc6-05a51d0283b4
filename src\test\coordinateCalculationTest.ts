// Manual test for coordinate-based calculation functionality
// This file can be used to verify that the dual-input system works correctly

import { calculerMoyensAeriensParCoordonnees } from '../services/calculService';
import { SimulatorSettings } from '../types';

// Test settings (using default values)
const testSettings: SimulatorSettings = {
  canadair: {
    vitesseCroisiere: 300,
    vitesseMaxLargage: 250,
    vitesseMinLargage: 180,
    capaciteReservoir: 6000,
    tempsRemplissage: 1,
    rayonBraquage: 800,
    altitudeMaxLargage: 150,
    altitudeMinLargage: 30,
    consommationCarburant: 1200,
    autonomieVol: 4.5
  },
  dash: {
    vitesseCroisiere: 400,
    vitesseMaxLargage: 320,
    vitesseMinLargage: 220,
    capaciteReservoir: 3000,
    tempsRemplissage: 3,
    rayonBraquage: 600,
    altitudeMaxLargage: 200,
    altitudeMinLargage: 50,
    consommationCarburant: 800,
    autonomieVol: 6
  },
  facteurVent: 1.0,
  facteurVisibilite: 1.0,
  tempsPreparationMission: 15,
  tempsRetourBase: 10,
  margeSecurite: 20,
  exigencesCanadair: 20,
  exigencesDash: 5,
  dureeMission: 60
};

// Test function to verify coordinate-based calculations
export async function testCoordinateCalculation() {
  try {
    console.log('🧪 Testing coordinate-based calculation...');
    
    // Test coordinates (somewhere in southern France)
    const testLatitude = 43.7750;
    const testLongitude = 6.2000;
    
    const params = {
      typeIntervention: 'incendie_foret' as const,
      exigencesCanadair: testSettings.exigencesCanadair,
      exigencesDash: testSettings.exigencesDash
    };

    const result = await calculerMoyensAeriensParCoordonnees(
      testLatitude,
      testLongitude,
      params,
      testSettings
    );

    console.log('✅ Coordinate calculation successful!');
    console.log('📍 Test coordinates:', { latitude: testLatitude, longitude: testLongitude });
    console.log('🔥 Virtual commune created:', result.commune);
    console.log('💧 Nearest water source:', result.planEauProche.nom, `(${result.distanceEau} km)`);
    console.log('✈️ Nearest base:', result.baseDashProche.nom, `(${result.distanceDash} km)`);
    console.log('🚁 Canadairs needed:', result.canadairsArrondi);
    console.log('🛩️ Dash needed:', result.dashsArrondi);

    return result;
  } catch (error) {
    console.error('❌ Coordinate calculation failed:', error);
    throw error;
  }
}

// Test function to verify that virtual commune creation works
export function testVirtualCommuneCreation() {
  console.log('🧪 Testing virtual commune creation...');
  
  const testLat = 43.7750;
  const testLon = 6.2000;
  
  // This function is now internal to calculService, but we can test the result
  const expectedId = `virtual_${testLat}_${testLon}`;
  
  console.log('✅ Expected virtual commune ID:', expectedId);
  console.log('📍 Coordinates:', { latitude: testLat, longitude: testLon });
  
  return {
    expectedId,
    coordinates: { latitude: testLat, longitude: testLon }
  };
}

// Run tests if this file is executed directly
if (typeof window === 'undefined') {
  // Node.js environment
  console.log('🚀 Running coordinate calculation tests...');
  testVirtualCommuneCreation();
  // Note: testCoordinateCalculation() requires Supabase connection
} else {
  // Browser environment
  console.log('🌐 Coordinate calculation test functions available in browser console');
  console.log('Run testCoordinateCalculation() to test with real data');
  console.log('Run testVirtualCommuneCreation() to test virtual commune creation');
}
